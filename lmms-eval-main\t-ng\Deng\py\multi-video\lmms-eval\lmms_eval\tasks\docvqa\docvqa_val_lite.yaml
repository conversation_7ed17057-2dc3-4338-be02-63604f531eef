task: "docvqa_val_lite"
test_split: lite
metric_list:
  - metric: anls
    aggregation: mean
    higher_is_better: true
dataset_path: lmms-lab/LMMs-Eval-Lite
dataset_name: docvqa_val
dataset_kwargs:
  token: True
output_type: generate_until
doc_to_visual: !function utils.docvqa_doc_to_visual
doc_to_text: !function utils.docvqa_doc_to_text
doc_to_target: "answers"
generation_kwargs:
  max_new_tokens: 32
  temperature: 0
  do_sample: False
lmms_eval_specific_kwargs:
  default:
    pre_prompt: ""
    post_prompt: "\nAnswer the question using a single word or phrase."
  qwen_vl:
    pre_prompt: ""
    post_prompt: " Answer:"

