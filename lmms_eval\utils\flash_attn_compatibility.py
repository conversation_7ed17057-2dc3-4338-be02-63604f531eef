"""
Flash Attention Compatibility Utilities
"""
import os
import sys
import warnings

def ensure_flash_attn_compatibility(force_disable: bool = False, verbose: bool = False):
    """
    Ensure flash_attn compatibility by setting up fallbacks and environment variables.
    """
    # Set environment variables to disable flash attention
    env_vars = {
        'DISABLE_FLASH_ATTN': '1',
        'TRANSFORMERS_DISABLE_FLASH_ATTN': '1',
        'FLASH_ATTENTION_DISABLE': '1',
        'TRANSFORMERS_ATTN_IMPLEMENTATION': 'eager'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
    
    if verbose:
        print("Flash attention disabled for compatibility")