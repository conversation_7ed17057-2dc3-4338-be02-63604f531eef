task: "cmmmu_val"
test_split: val
# The return value of process_results will be used by metrics
process_results: !function utils.cmmmu_process_results
# Note that the metric name can be either a registed metric function (such as the case for GQA) or a key name returned by process_results
generation_kwargs:
  max_new_tokens: 16
  image_aspect_ratio: original
metric_list:
  - metric: cmmmu_acc
    aggregation: !function utils.cmmmu_aggregate_results
    higher_is_better: true
metadata:
  - version: 0.0
include: _default_template_cmmmu_yaml
