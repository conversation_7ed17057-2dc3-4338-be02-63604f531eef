dataset_path: /t-ng/multi-video/mvr_dataset
dataset_kwargs:
  token: True
  cache_dir: /t-ng/multi-video/test
  video: True
  load_from_disk: True
  #data_dir: ../../../dataset/dataset.csv
  # From_YouTube: True
task: "mvr"
test_split: test
output_type: generate_until
doc_to_visual: !function utils.mvr_doc_to_visual
doc_to_text: !function utils.mvr_doc_to_text
doc_to_target: "answer"
generation_kwargs:
  max_new_tokens: 16
  temperature: 0
  top_p: 1.0
  num_beams: 1
  do_sample: false
# The return value of process_results will be used by metrics
process_results: !function utils.mvr_process_results
# Note that the metric name can be either a registed metric function (such as the case for GQA) or a key name returned by process_results
metric_list:
  - metric: mvr_perception_score
    aggregation: !function utils.mvr_aggregate_results
    higher_is_better: true
lmms_eval_specific_kwargs:
  default:
    pre_prompt: ""
    post_prompt1: "Answer with the option's letter (A, B, C, or D) from the given choices directly."
    post_prompt2: "Answer with the option's word (YES or NO) from the given choices directly."
  # gpt4v:
  #   pre_prompt: ""
  #   post_prompt: "Answer the question with the letter (A, B, C, or D) or the word (YES or NO)."
  # llava_vid:
  #   pre_prompt: ""
  #   post_prompt: "Answer with the option's letter (A, B, C, or D) or the word (YES or NO) from the given choices directly."
  # qwen_vl:  
  #   pre_prompt: ""
  #   post_prompt: " Answer:"
  # otterhd:
  #   pre_prompt: ""
  #   post_prompt: " Answer:"
  # xcomposer2_4khd:
  #   pre_prompt: "[UNUSED_TOKEN_146]user\n"
  #   post_prompt: " Answer this question with the letter (A, B, C, or D) or the word (YES or NO).[UNUSED_TOKEN_145]\n[UNUSED_TOKEN_146]assistant\n"
metadata:
  - version: 0.0
