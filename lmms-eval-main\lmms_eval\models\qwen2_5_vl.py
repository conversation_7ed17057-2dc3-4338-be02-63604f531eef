import base64
import os
import re
from io import BytesIO
from typing import List, Optional, Tuple, Union

import decord
import numpy as np
import torch
from accelerate import Accelerator, DistributedType
from loguru import logger as eval_logger
from PIL import Image
from tqdm import tqdm
from transformers import (
    AutoProcessor,
    AutoTokenizer,
    Qwen2_5_VLForConditionalGeneration,
)

from lmms_eval import utils
from lmms_eval.api.instance import Instance
from lmms_eval.api.model import lmms
from lmms_eval.api.registry import register_model
from lmms_eval.models.model_utils.load_video import read_video_pyav_base64, read_video_pyav

try:
    from qwen_vl_utils import process_vision_info
except ImportError:
    eval_logger.warning("Failed to import qwen_vl_utils; Please install it via `pip install qwen-vl-utils`")


@register_model("qwen2_5_vl")
class Qwen2_5_VL(lmms):
    """
    Qwen2.5_VL Model
    "https://huggingface.co/Qwen/Qwen2.5-VL-7B-Instruct"
    """

    def __init__(
        self,
        pretrained: str = "Qwen/Qwen2.5-VL-7B-Instruct",
        device: Optional[str] = "cuda",
        device_map: Optional[str] = "auto",
        batch_size: Optional[Union[int, str]] = 1,
        use_cache=True,
        attn_implementation: Optional[str] = None,
        min_pixels: int = 147456,  # 384 * 384 for 384x384 resolution
        max_pixels: int = 589824,  # 384 * 384 * 4 for higher quality
        max_num_frames: int = 32,
        use_custom_video_loader: Optional[bool] = False,
        fps: Optional[float] = None,  # Only applicable if use_custom_video_loader is True
        max_image_size: Optional[int] = None,  # Only applicable if use_custom_video_loader is True
        system_prompt: Optional[str] = "You are a helpful assistant.",
        interleave_visuals: Optional[bool] = False,
        reasoning_prompt: Optional[str] = None,
        **kwargs,
    ) -> None:
        super().__init__()
        # Do not use kwargs for now
        assert kwargs == {}, f"Unexpected kwargs: {kwargs}"

        # Validate attention implementation
        valid_attn_implementations = [None, "flash_attention_2", "sdpa", "eager"]
        if attn_implementation not in valid_attn_implementations:
            raise ValueError(f"attn_implementation must be one of {valid_attn_implementations}, got {attn_implementation}")

        self.use_custom_video_loader = use_custom_video_loader
        self.fps = fps
        # if self.fps and not self.use_custom_video_loader:
        #     raise ValueError("FPS is only applicable if use_custom_video_loader is True")
        self.max_image_size = max_image_size
        if self.max_image_size and not self.use_custom_video_loader:
            raise ValueError("max_image_size is only applicable if use_custom_video_loader is True")

        accelerator = Accelerator()
        if accelerator.num_processes > 1:
            self._device = torch.device(f"cuda:{accelerator.local_process_index}")
            self.device_map = f"cuda:{accelerator.local_process_index}"
        else:
            self._device = torch.device(device)
            self.device_map = device_map if device_map else device

        # Prepare model loading arguments
        model_kwargs = {
            "torch_dtype": "auto",
            "device_map": self.device_map,
        }

        # Add attention implementation if specified
        if attn_implementation is not None:
            model_kwargs["attn_implementation"] = attn_implementation

        self._model = Qwen2_5_VLForConditionalGeneration.from_pretrained(pretrained, **model_kwargs).eval()
        self.max_pixels = max_pixels
        self.min_pixels = min_pixels
        self.max_num_frames = max_num_frames

        if reasoning_prompt:
            self.reasoning_prompt = reasoning_prompt.replace("\\n", "\n")
        else:
            self.reasoning_prompt = None
        self.processor = AutoProcessor.from_pretrained(pretrained, max_pixels=max_pixels, min_pixels=min_pixels)
        self._tokenizer = AutoTokenizer.from_pretrained(pretrained)
        self.system_prompt = system_prompt
        self.interleave_visuals = interleave_visuals

        self._config = self.model.config
        self._max_length = kwargs.get("max_length", 2048)
        self.batch_size_per_gpu = int(batch_size)
        self.use_cache = use_cache

        if accelerator.num_processes > 1:
            assert accelerator.distributed_type in [
                DistributedType.FSDP,
                DistributedType.MULTI_GPU,
            ], "Unsupported distributed type provided. Only DDP and FSDP are supported."
            if accelerator.distributed_type == DistributedType.FSDP:
                self._model = accelerator.prepare(self.model)
            else:
                self._model = accelerator.prepare_model(self.model, evaluation_mode=True)
            self.accelerator = accelerator
            if self.accelerator.is_local_main_process:
                eval_logger.info(f"Using {accelerator.num_processes} devices with data parallelism")
            self._rank = self.accelerator.local_process_index
            self._world_size = self.accelerator.num_processes
        else:
            self._rank = 0
            self._world_size = 1

    @property
    def config(self):
        # return the associated transformers.AutoConfig for the given pretrained model.
        return self._config

    @property
    def tokenizer(self):
        return self._tokenizer

    @property
    def model(self):
        # returns the model, unwrapping it if using Accelerate
        if hasattr(self, "accelerator"):
            return self.accelerator.unwrap_model(self._model)
        else:
            return self._model

    @property
    def eot_token_id(self):
        return self.tokenizer.eos_token_id

    @property
    def max_length(self):
        return self._max_length

    @property
    def batch_size(self):
        return self.batch_size_per_gpu

    @property
    def device(self):
        return self._device

    @property
    def rank(self):
        return self._rank

    @property
    def world_size(self):
        return self._world_size

    def loglikelihood(self, requests: List[Instance]) -> List[Tuple[float, bool]]:
        raise NotImplementedError("Loglikelihood is not implemented for Qwen2.5_VL")

    def flatten(self, input):
        new_list = []
        for i in input:
            for j in i:
                new_list.append(j)
        return new_list

    def _process_multi_videos_with_markers(self, video_paths, num_frm_per_video=8, target_size=(224, 224)):
        """
        Process multiple videos (up to 4) with marker frames for Qwen2.5-VL.
        Each video contributes 8 frames + 2 marker frames (cover and end).

        Args:
            video_paths: List of video file paths (up to 4 videos)
            num_frm_per_video: Number of frames to extract from each video (default: 8)
            target_size: Target size for frames (default: (224, 224))

        Returns:
            List of processed visual dictionaries for Qwen2.5-VL
        """
        all_visuals = []
        tips_dir = "/t-ng/multi-video/lmms-eval-main/tips"

        # Limit to maximum 4 videos as requested
        max_videos = 4
        valid_videos = [v for v in video_paths if v is not None][:max_videos]

        eval_logger.info(f"Processing {len(valid_videos)} videos with {num_frm_per_video} frames each")

        for i, video_path in enumerate(valid_videos):
            try:
                # Extract video frames with memory optimization
                video_frames = read_video_pyav(video_path, num_frm=num_frm_per_video)
                eval_logger.info(f"Extracted {len(video_frames)} frames from video {i+1}")

                # Load cover marker frame
                cover_path = os.path.join(tips_dir, f"{i+1}.png")
                if os.path.exists(cover_path):
                    cover_img = Image.open(cover_path).convert("RGB").resize(target_size)
                    buffer = BytesIO()
                    cover_img.save(buffer, format="JPEG", quality=85)
                    base64_bytes = base64.b64encode(buffer.getvalue())
                    base64_string = base64_bytes.decode("utf-8")
                    all_visuals.append({
                        "type": "image",
                        "image": f"data:image/jpeg;base64,{base64_string}",
                        "max_pixels": self.max_pixels,
                        "min_pixels": self.min_pixels
                    })
                    buffer.close()

                # Process video frames with memory optimization
                for frame in video_frames:
                    img = Image.fromarray(frame).resize(target_size)
                    buffer = BytesIO()
                    img.save(buffer, format="JPEG", quality=85)
                    base64_bytes = base64.b64encode(buffer.getvalue())
                    base64_string = base64_bytes.decode("utf-8")
                    all_visuals.append({
                        "type": "image",
                        "image": f"data:image/jpeg;base64,{base64_string}",
                        "max_pixels": self.max_pixels,
                        "min_pixels": self.min_pixels
                    })
                    buffer.close()

                # Load end marker frame
                end_path = os.path.join(tips_dir, f"{i+1}e.png")
                if os.path.exists(end_path):
                    end_img = Image.open(end_path).convert("RGB").resize(target_size)
                    buffer = BytesIO()
                    end_img.save(buffer, format="JPEG", quality=85)
                    base64_bytes = base64.b64encode(buffer.getvalue())
                    base64_string = base64_bytes.decode("utf-8")
                    all_visuals.append({
                        "type": "image",
                        "image": f"data:image/jpeg;base64,{base64_string}",
                        "max_pixels": self.max_pixels,
                        "min_pixels": self.min_pixels
                    })
                    buffer.close()

                # Clear video frames from memory
                del video_frames

            except Exception as e:
                eval_logger.error(f"Error processing video {i+1}: {e}")
                continue

        eval_logger.info(f"Total frames processed: {len(all_visuals)}")
        return all_visuals

    def _is_multi_video_input(self, visuals):
        """
        Check if the input contains multiple videos (typical for MVR tasks).

        Args:
            visuals: List of visual inputs

        Returns:
            bool: True if this is a multi-video input
        """
        if not isinstance(visuals, list) or len(visuals) <= 1:
            return False

        # Check if all items are video file paths
        return all(isinstance(v, str) and v.endswith((".mp4", ".avi", ".mov")) for v in visuals)

    def _format_multi_video_prompt(self, context):
        """
        Format the prompt for multi-video input with special instructions.

        Args:
            context: Original context/question

        Returns:
            str: Formatted prompt with multi-video instructions
        """
        multi_video_prompt = f"""<Videos>
Select the best answer to the following question based on the listed all videos. Respond with only the word (Yes or No) or the option's letter (A, B, C, or D) from the given choices directly.

Please pay close attention to the video frames with special cues that are interspersed at the beginning and end of a video's content. For example, frames with the words "The video X" represent the beginning of the video called video X, and frames with the words "Video X End" represent the end of the video called video X.

<Question>
{context}
</Videos>"""
        return multi_video_prompt

    def generate_until(self, requests: List[Instance]) -> List[str]:
        res = []

        def _collate(x):
            # the negative sign on len(toks) sorts descending - this has a few advantages:
            # - time estimates will always be over not underestimates, which is more useful for planning
            # - to know the size of a batch when going through the list, you know the first one is always the batch
            #   padded context length. this is useful to simplify the batching logic and more importantly to make
            #   automatic adaptive batches much much easier to implement
            # - any OOMs will happen right away rather than near the end
            toks = self.tokenizer.encode(x[0])
            return -len(toks), x[0]

        pbar = tqdm(total=len(requests), disable=(self.rank != 0), desc="Model Responding")
        # we group requests by their generation_kwargs,
        # so that we don't try to execute e.g. greedy sampling and temp=0.8 sampling
        # in the same batch.
        re_ords = utils.Collator([reg.args for reg in requests], _collate, grouping=True)
        chunks = re_ords.get_batched(n=self.batch_size, batch_fn=None)
        for chunk in chunks:
            contexts, all_gen_kwargs, doc_to_visual, doc_id, task, split = zip(*chunk)
            task = task[0]
            split = split[0]

            # Handle DatasetDict vs Dataset compatibility
            ds = self.task_dict[task]
            if isinstance(ds, dict) or hasattr(ds, "keys"):
                visual_list = [doc_to_visual[0](ds[split][ids]) for ids in doc_id]
            else:
                visual_list = [doc_to_visual[0](ds[ids]) for ids in doc_id]
            gen_kwargs = all_gen_kwargs[0]

            # Set default until or update values from gen_kwargs if present
            until = gen_kwargs.get("until", [self.tokenizer.decode(self.eot_token_id)])

            if isinstance(until, str):
                until = [until]
            elif not isinstance(until, list):
                raise ValueError(f"Expected `gen_kwargs['until']` to be of type Union[str, list], but got {type(until)}")

            # Avoid using '\n\n' as a stopper for Qwen2.5VL to prevent truncation, which can lead to incorrect results
            until = [item for item in until if item != "\n\n"]

            if isinstance(contexts, tuple):
                contexts = list(contexts)

            for i in range(len(contexts)):
                if "<image>" in contexts[i]:
                    contexts[i] = contexts[i].replace("<image>", "")

            batched_messages = []
            for i, context in enumerate(contexts):
                if "<image>" in context:
                    context = context.replace("<image>", "")

                message = [{"role": "system", "content": self.system_prompt}]
                if self.reasoning_prompt:
                    context = context.strip() + self.reasoning_prompt
                    contexts[i] = context

                processed_visuals = []

                # Check for multi-video input (e.g., MVR tasks)
                if self._is_multi_video_input(visual_list[i]):
                    # Process multiple videos with marker frames
                    eval_logger.info(f"Multi-video input detected: {len(visual_list[i])} videos")
                    eval_logger.info(f"Original context: {context[:100]}...")
                    processed_visuals = self._process_multi_videos_with_markers(visual_list[i], num_frm_per_video=8, target_size=(224, 224))
                    # Use formatted multi-video prompt
                    context = self._format_multi_video_prompt(context)
                    contexts[i] = context
                    eval_logger.info(f"Updated context: {context[:200]}...")
                    eval_logger.info("Multi-video prompt formatting applied successfully")
                else:
                    # Original single video/image processing
                    for visual in visual_list[i]:
                        if isinstance(visual, str) and visual.endswith((".mp4", ".avi", ".mov")):  # Single video file
                            if self.use_custom_video_loader:
                                # Use custom video loader to extract frames as images
                                try:
                                    video_frames = read_video_pyav(visual, num_frm=self.max_num_frames, fps=self.fps)
                                    for frame in video_frames:
                                        img = Image.fromarray(frame)
                                        buffer = BytesIO()
                                        img.save(buffer, format="JPEG", quality=85)
                                        base64_bytes = base64.b64encode(buffer.getvalue())
                                        base64_string = base64_bytes.decode("utf-8")
                                        processed_visuals.append({
                                            "type": "image",
                                            "image": f"data:image/jpeg;base64,{base64_string}",
                                            "max_pixels": self.max_pixels,
                                            "min_pixels": self.min_pixels
                                        })
                                        buffer.close()
                                except Exception as e:
                                    eval_logger.error(f"Error processing video {visual}: {e}")
                                    # Fallback to original video processing
                                    processed_visuals.append({"type": "video", "video": visual, "max_pixels": self.max_pixels, "min_pixels": self.min_pixels})
                            else:
                                # Original video processing
                                processed_visuals.append({"type": "video", "video": visual, "max_pixels": self.max_pixels, "min_pixels": self.min_pixels})
                        elif isinstance(visual, Image.Image):  # Handle both single and multiple images
                            base64_image = visual.convert("RGB")
                            buffer = BytesIO()
                            base64_image.save(buffer, format="JPEG")
                            base64_bytes = base64.b64encode(buffer.getvalue())
                            base64_string = base64_bytes.decode("utf-8")
                            processed_visuals.append({"type": "image", "image": f"data:image/jpeg;base64,{base64_string}", "max_pixels": self.max_pixels, "min_pixels": self.min_pixels})
                            buffer.close()

                if self.interleave_visuals is False:
                    message.append(
                        {
                            "role": "user",
                            "content": processed_visuals + [{"type": "text", "text": context}],
                        }
                    )
                else:  # currently support find <image x> in the context
                    image_placeholders = re.findall(r"<image \d+>", context)
                    content_parts = []
                    text_parts = re.split(r"<image \d+>", context)
                    if text_parts[0]:
                        content_parts.append({"type": "text", "text": text_parts[0]})

                    for i, placeholder in enumerate(image_placeholders):
                        img_idx = int(re.search(r"<image (\d+)>", placeholder).group(1)) - 1
                        image_idx = min(img_idx, len(processed_visuals) - 1) if processed_visuals else 0
                        if processed_visuals and image_idx < len(processed_visuals):
                            content_parts.append(processed_visuals[image_idx])
                        if i + 1 < len(text_parts) and text_parts[i + 1]:
                            content_parts.append({"type": "text", "text": text_parts[i + 1]})

                    message.append(
                        {
                            "role": "user",
                            "content": content_parts,
                        }
                    )

                batched_messages.append(message)

            texts = [self.processor.apply_chat_template(msg, tokenize=False, add_generation_prompt=True) for msg in batched_messages]
            image_inputs, video_inputs = process_vision_info(batched_messages)
            if video_inputs is not None:
                total_frames = video_inputs[0].shape[0]
                indices = np.linspace(0, total_frames - 1, self.max_num_frames, dtype=int)
                # Append the last frame index if not already included
                if total_frames - 1 not in indices:
                    indices = np.append(indices, total_frames - 1)
                video_inputs[0] = video_inputs[0][indices]
            inputs = self.processor(text=texts, images=image_inputs, videos=video_inputs, padding=True, return_tensors="pt")

            if self.device_map == "auto":
                inputs = inputs.to("cuda")
            else:
                inputs = inputs.to(self.device)

            # Set default generation kwargs
            default_gen_kwargs = {
                "max_new_tokens": 128,
                "temperature": 0.0,  # Set to 0 for greedy default
                "top_p": None,
                "num_beams": 1,
            }
            # Update with provided kwargs
            current_gen_kwargs = {**default_gen_kwargs, **gen_kwargs}
            pad_token_id = self.tokenizer.pad_token_id

            if current_gen_kwargs["temperature"] > 0:
                current_gen_kwargs["do_sample"] = True
            else:
                current_gen_kwargs["do_sample"] = False
                current_gen_kwargs["temperature"] = None
                current_gen_kwargs["top_p"] = None

            try:
                # Clear GPU cache before generation
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

                cont = self.model.generate(
                    **inputs,
                    eos_token_id=self.tokenizer.eos_token_id,
                    pad_token_id=pad_token_id,
                    do_sample=current_gen_kwargs["do_sample"],
                    temperature=current_gen_kwargs["temperature"],
                    top_p=current_gen_kwargs["top_p"],
                    num_beams=current_gen_kwargs["num_beams"],
                    max_new_tokens=current_gen_kwargs["max_new_tokens"],
                    use_cache=self.use_cache,
                )
            except torch.cuda.OutOfMemoryError as e:
                eval_logger.error(f"CUDA OOM Error: {e}")
                # Clear cache and try with reduced settings
                torch.cuda.empty_cache()
                eval_logger.info("Retrying with reduced max_new_tokens...")
                try:
                    cont = self.model.generate(
                        **inputs,
                        eos_token_id=self.tokenizer.eos_token_id,
                        pad_token_id=pad_token_id,
                        do_sample=False,  # Disable sampling to save memory
                        num_beams=1,      # Reduce beam search
                        max_new_tokens=min(current_gen_kwargs["max_new_tokens"], 64),  # Reduce max tokens
                        use_cache=False,  # Disable cache to save memory
                    )
                except Exception as retry_e:
                    eval_logger.error(f"Retry failed: {retry_e}")
                    # Return empty responses for this batch
                    res.extend([""] * len(contexts))
                    pbar.update(len(contexts))
                    continue
            except Exception as e:
                eval_logger.error(f"Error in generation: {e}")
                # Return empty responses for this batch
                res.extend([""] * len(contexts))
                pbar.update(len(contexts))
                continue

            generated_ids_trimmed = [out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, cont)]
            answers = self.processor.batch_decode(generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False)
            for i, ans in enumerate(answers):
                for term in until:
                    if len(term) > 0:
                        ans = ans.split(term)[0]
                answers[i] = ans

            for ans, context in zip(answers, contexts):
                res.append(ans)
                self.cache_hook.add_partial("generate_until", (context, gen_kwargs), ans)
                pbar.update(1)
            # reorder this group of results back to original unsorted form
        res = re_ords.get_original(res)

        pbar.close()
        return res

    def generate_until_multi_round(self, requests) -> List[str]:
        raise NotImplementedError("TODO: Implement multi-round generation")
