task: "clotho_aqa_test"
dataset_name: "clotho_aqa"
test_split: clotho_aqa_test_filtered
generation_kwargs:
  max_new_tokens: 8
  temperature: 0
  do_sample: False
lmms_eval_specific_kwargs:
  default:
    pre_prompt: ""
    post_prompt: "\nAnswer the question using a single word only. "
metric_list:
  - metric: exact_match
    aggregation: mean
    higher_is_better: true
    ignore_case: true
    ignore_punctuation: true

include: _default_template_yaml
