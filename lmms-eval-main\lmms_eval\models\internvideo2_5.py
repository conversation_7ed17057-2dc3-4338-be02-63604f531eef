import logging
import os
import sys
from datetime import timed<PERSON><PERSON>
from typing import List, Tuple

# Apply InternVL fixes BEFORE any transformers imports
# Set environment variables first
os.environ['DISABLE_FLASH_ATTN'] = '1'
os.environ['TRANSFORMERS_DISABLE_FLASH_ATTN'] = '1'
os.environ['FLASH_ATTENTION_DISABLE'] = '1'
os.environ['TRANSFORMERS_ATTN_IMPLEMENTATION'] = 'eager'

# Try to import the preload fix
try:
    # Add current directory to path to find preload_internvl_fix
    current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    import preload_internvl_fix
    eval_logger = logging.getLogger("eval_logger")
    eval_logger.info("✓ InternVL preload fix applied")
except ImportError as e:
    eval_logger = logging.getLogger("eval_logger")
    eval_logger.warning(f"Could not import preload_internvl_fix: {e}")

import numpy as np
import torch
import torchvision.transforms as T
from accelerate import Accelerator, DistributedType
from accelerate.state import AcceleratorState
from accelerate.utils import InitProcessGroupKwargs
from decord import VideoReader, cpu
from PIL import Image
from torchvision.transforms.functional import InterpolationMode
from tqdm import tqdm

# Ensure flash attention compatibility before importing transformers
try:
    from lmms_eval.utils.flash_attn_compatibility import ensure_flash_attn_compatibility
    ensure_flash_attn_compatibility()
except ImportError:
    # If the compatibility module doesn't exist, continue without it
    pass

from transformers import AutoModel, AutoTokenizer

from lmms_eval.api.instance import Instance
from lmms_eval.api.model import lmms
from lmms_eval.api.registry import register_model

eval_logger = logging.getLogger("eval_logger")

IMAGENET_MEAN = (0.485, 0.456, 0.406)
IMAGENET_STD = (0.229, 0.224, 0.225)

DEFAULT_GEN_KWARGS = dict(
    num_beams=1,
    max_new_tokens=1024,
    do_sample=False,
)


def build_transform(input_size):
    MEAN, STD = IMAGENET_MEAN, IMAGENET_STD
    transform = T.Compose([T.Lambda(lambda img: img.convert("RGB") if img.mode != "RGB" else img), T.Resize((input_size, input_size), interpolation=InterpolationMode.BICUBIC), T.ToTensor(), T.Normalize(mean=MEAN, std=STD)])
    return transform


def find_closest_aspect_ratio(aspect_ratio, target_ratios, width, height, image_size):
    best_ratio_diff = float("inf")
    best_ratio = (1, 1)
    area = width * height
    for ratio in target_ratios:
        target_aspect_ratio = ratio[0] / ratio[1]
        ratio_diff = abs(aspect_ratio - target_aspect_ratio)
        if ratio_diff < best_ratio_diff:
            best_ratio_diff = ratio_diff
            best_ratio = ratio
        elif ratio_diff == best_ratio_diff:
            if area > 0.5 * image_size * image_size * ratio[0] * ratio[1]:
                best_ratio = ratio
    return best_ratio


def dynamic_preprocess(image, min_num=1, max_num=6, image_size=448, use_thumbnail=False):
    orig_width, orig_height = image.size
    aspect_ratio = orig_width / orig_height

    # calculate the existing image aspect ratio
    target_ratios = set((i, j) for n in range(min_num, max_num + 1) for i in range(1, n + 1) for j in range(1, n + 1) if i * j <= max_num and i * j >= min_num)
    target_ratios = sorted(target_ratios, key=lambda x: x[0] * x[1])

    # find the closest aspect ratio to the target
    target_aspect_ratio = find_closest_aspect_ratio(aspect_ratio, target_ratios, orig_width, orig_height, image_size)

    # calculate the target width and height
    target_width = image_size * target_aspect_ratio[0]
    target_height = image_size * target_aspect_ratio[1]
    blocks = target_aspect_ratio[0] * target_aspect_ratio[1]

    # resize the image
    resized_img = image.resize((target_width, target_height))
    processed_images = []
    for i in range(blocks):
        box = ((i % (target_width // image_size)) * image_size, (i // (target_width // image_size)) * image_size, ((i % (target_width // image_size)) + 1) * image_size, ((i // (target_width // image_size)) + 1) * image_size)
        # split the image
        split_img = resized_img.crop(box)
        processed_images.append(split_img)
    assert len(processed_images) == blocks
    if use_thumbnail and len(processed_images) != 1:
        thumbnail_img = image.resize((image_size, image_size))
        processed_images.append(thumbnail_img)
    return processed_images


def load_image(image, input_size=448, max_num=6):
    transform = build_transform(input_size=input_size)
    images = dynamic_preprocess(image, image_size=input_size, use_thumbnail=True, max_num=max_num)
    pixel_values = [transform(image) for image in images]
    pixel_values = torch.stack(pixel_values)
    return pixel_values


def get_index(bound, fps, max_frame, first_idx=0, num_segments=32):
    if bound:
        start, end = bound[0], bound[1]
    else:
        start, end = -100000, 100000
    start_idx = max(first_idx, round(start * fps))
    end_idx = min(round(end * fps), max_frame)
    seg_size = float(end_idx - start_idx) / num_segments
    frame_indices = [int(start_idx + (seg_size / 2) + np.round(seg_size * idx)) for idx in range(num_segments)]
    return frame_indices


def load_video(video_path, bound=None, input_size=448, max_num=1, num_segments=32, media_dict=None):
    if type(video_path) == str:
        vr = VideoReader(video_path, ctx=cpu(0), num_threads=1)
    else:
        vr = VideoReader(video_path[0], ctx=cpu(0), num_threads=1)

    max_frame = len(vr) - 1
    fps = float(vr.get_avg_fps())

    pixel_values_list, num_patches_list = [], []
    transform = build_transform(input_size=input_size)
    frame_indices = get_index(bound, fps, max_frame, first_idx=0, num_segments=num_segments)
    if media_dict is not None and media_dict["video_read_type"] == "decord_last":
        frame_indices = frame_indices + [max_frame for _ in range(4)]  # add last 4 frames

    frame_indices = np.array(frame_indices)

    for frame_index in frame_indices:
        img = Image.fromarray(vr[frame_index].asnumpy()).convert("RGB")
        img = dynamic_preprocess(img, image_size=input_size, use_thumbnail=False, max_num=max_num)
        pixel_values = [transform(tile) for tile in img]
        pixel_values = torch.stack(pixel_values)
        num_patches_list.append(pixel_values.shape[0])
        pixel_values_list.append(pixel_values)
    pixel_values = torch.cat(pixel_values_list)
    return pixel_values, num_patches_list


@register_model("internvideo2_5")
class InternVideo2_5(lmms):
    def __init__(
        self,
        pretrained: str = "OpenGVLab/InternVideo2_5_Chat_8B",
        modality: str = "video",
        device: str = "cuda:0",
        device_map: str = "cuda:0",
        batch_size: str = "1",
        max_frames_num: int = 6,
        **kwargs,
    ):
        super().__init__()
        self.max_frames_num = max_frames_num
        self.path = pretrained
        # Try loading with different configurations to handle various compatibility issues
        model_loaded = False
        last_error = None

        # Configuration attempts in order of preference
        # Include different revisions to handle config file issues
        configs = [
            {"use_flash_attn": True, "torch_dtype": torch.bfloat16},
            {"use_flash_attn": False, "torch_dtype": torch.bfloat16},
            {"use_flash_attn": False, "torch_dtype": torch.bfloat16, "revision": "main"},
            {"torch_dtype": torch.bfloat16},  # Without use_flash_attn parameter
            {"torch_dtype": "auto"},  # Let transformers decide the dtype
            {"torch_dtype": "auto", "revision": "main"},  # Try with main revision
        ]

        for i, config in enumerate(configs):
            try:
                eval_logger.info(f"Attempting to load model with config {i+1}/{len(configs)}: {config}")
                self._model = AutoModel.from_pretrained(
                    self.path,
                    low_cpu_mem_usage=True,
                    trust_remote_code=True,
                    **config
                ).eval().cuda()
                model_loaded = True
                eval_logger.info(f"Successfully loaded model with config: {config}")
                break
            except Exception as e:
                last_error = e
                eval_logger.warning(f"Failed to load with config {config}: {e}")
                continue

        if not model_loaded:
            eval_logger.error(f"Failed to load model with all configurations. Last error: {last_error}")
            raise last_error
        self._tokenizer = AutoTokenizer.from_pretrained(self.path, trust_remote_code=True)

        batch_size = int(batch_size)
        assert batch_size == 1, f"Batch size should be 1 for InternVL2, but got {batch_size}."
        self.batch_size_per_gpu = batch_size

        accelerator_kwargs = InitProcessGroupKwargs(timeout=timedelta(weeks=52))
        accelerator = Accelerator(kwargs_handlers=[accelerator_kwargs])
        self.accelerator = accelerator
        if accelerator.num_processes > 1:
            self._device = torch.device(f"cuda:{accelerator.local_process_index}")
            self.device_map = f"cuda:{accelerator.local_process_index}"
        elif accelerator.num_processes == 1 and device_map == "auto":
            self._device = torch.device(device)
            self.device_map = device_map
        else:
            self._device = torch.device(f"cuda:{accelerator.local_process_index}")
            self.device_map = f"cuda:{accelerator.local_process_index}"

        if accelerator.num_processes > 1:
            assert accelerator.distributed_type in [DistributedType.FSDP, DistributedType.MULTI_GPU, DistributedType.DEEPSPEED], "Unsupported distributed type provided. Only DDP and FSDP are supported."
            # If you want to use DistributedType.DEEPSPEED, you have to run accelerate config before using the model
            # Also, you have to select zero stage 0 (equivalent to DDP) in order to make the prepare model works
            # I tried to set different parameters in the kwargs to let default zero 2 stage works, but it didn't work.
            if accelerator.distributed_type == DistributedType.DEEPSPEED:
                kwargs = {
                    "train_micro_batch_size_per_gpu": self.batch_size_per_gpu,
                    "train_batch_size": self.batch_size_per_gpu * accelerator.num_processes,
                }
                AcceleratorState().deepspeed_plugin.deepspeed_config_process(must_match=True, **kwargs)
                eval_logger.info("Detected that you are using DistributedType.DEEPSPEED. Make sure you run `accelerate config` and set zero stage to 0")

            if accelerator.distributed_type == DistributedType.FSDP or accelerator.distributed_type == DistributedType.DEEPSPEED:
                self._model = accelerator.prepare(self.model)
            else:
                self._model = accelerator.prepare_model(self.model, evaluation_mode=True)
            self.accelerator = accelerator
            if self.accelerator.is_local_main_process:
                eval_logger.info(f"Using {accelerator.num_processes} devices with data parallelism")
            self._rank = self.accelerator.local_process_index
            self._world_size = self.accelerator.num_processes
        elif accelerator.num_processes == 1 and device_map == "auto":
            eval_logger.info(f"Using {accelerator.num_processes} devices with tensor parallelism")
            self._rank = 0
            self._word_size = 1
        else:
            eval_logger.info(f"Using single device: {self._device}")
            self.model.to(self._device)
            self._rank = 0
            self._world_size = 1

        self.modality = modality

    @property
    def config(self):
        # return the associated transformers.AutoConfig for the given pretrained model.
        return self._config

    @property
    def tokenizer(self):
        return self._tokenizer

    @property
    def model(self):
        # returns the model, unwrapping it if using Accelerate
        if hasattr(self, "accelerator"):
            return self.accelerator.unwrap_model(self._model)
        else:
            return self._model

    @property
    def batch_size(self):
        return self.batch_size_per_gpu

    @property
    def device(self):
        return self._device

    @property
    def rank(self):
        return self._rank

    @property
    def world_size(self):
        return self._world_size

    def flatten(self, input):
        new_list = []
        for i in input:
            for j in i:
                new_list.append(j)
        return new_list

    def generate_until(self, requests) -> List[str]:
        res = []
        pbar = tqdm(total=len(requests), disable=(self.rank != 0), desc="Model Responding")

        for contexts, gen_kwargs, doc_to_visual, doc_id, task, split in [reg.args for reg in requests]:
            if "until" in gen_kwargs:
                gen_kwargs.pop("until")
            for k, v in DEFAULT_GEN_KWARGS.items():
                if k not in gen_kwargs:
                    gen_kwargs[k] = v

            pop_keys = []
            for k, v in gen_kwargs.items():
                if k not in DEFAULT_GEN_KWARGS:
                    pop_keys.append(k)

            for k in pop_keys:
                gen_kwargs.pop(k)

            visuals = [doc_to_visual(self.task_dict[task][split][doc_id])]
            visuals = self.flatten(visuals)
            if self.modality == "image":
                if visuals:
                    visuals = [load_image(visual).to(torch.bfloat16).cuda() for visual in visuals]
                    pixel_values = torch.cat(visuals, dim=0)
                    num_patches_list = [visual.size(0) for visual in visuals]
                    image_tokens = ["<image>"] * len(visuals)
                    image_tokens = " ".join(image_tokens)
                    contexts = image_tokens + "\n" + contexts
                else:
                    pixel_values = None
                    num_patch_list = None
                response, history = self.model.chat(self.tokenizer, pixel_values, contexts, gen_kwargs, num_patches_list=num_patches_list, history=None, return_history=True)
            elif self.modality == "video":
                # assert len(visuals) == 1, f"Only one video is supported, but got {len(visuals)} videos. {visuals}"
                start_img_path = "/root/autodl-tmp/lmms-eval-main/res/video"
                end_img_path = "/root/autodl-tmp/lmms-eval-main/res/end"
                if len(visuals) > 1:
                    pixels_list = []
                    num_patches_lists = []
                    print("Loading video frames...",visuals)
                    for j in range(len(visuals)):
                        start_img = start_img_path + f"{j+1}.png"
                        end_img = end_img_path + f"{j+1}.png"
                        video_path = visuals[j]
                        start = load_image(Image.open(start_img),input_size=448,max_num=1)
                        end = load_image(Image.open(end_img),input_size=448,max_num=1)
                        pixel_values, num_patches_list = load_video(video_path, num_segments=self.max_frames_num, max_num=1,input_size=448)
                        pixel_values = torch.cat([start, pixel_values, end], dim=0)
                        pixels_list.append(pixel_values.to(torch.bfloat16).cuda())
                        num_patches_lists = num_patches_lists + [1] + num_patches_list +[1]
                    all_pixels = torch.cat(pixels_list,dim=0)
                    video_prefix = "".join([f"Frame{i+1}: <image>\n" for i in range(len(num_patches_lists))])
                    question = video_prefix+"Please pay close attention to the video frames with special cues that are interspersed at the beginning and end of a video's content. For example, frames with the words \"The video X\" represent the beginning of the video called video X, and frames with the words \"Video X End\" represent the end of the video called video X." + contexts
                    response, history = self.model.chat(self.tokenizer, all_pixels, question, gen_kwargs, num_patches_list=num_patches_lists, history=None, return_history=True)
                else:
                    video_path = visuals[0]
                    media_dict = {"video_read_type": "decord"}
                    pixel_values, num_patches_list = load_video(video_path, num_segments=self.max_frames_num, max_num=1, media_dict=media_dict)
                    pixel_values = pixel_values.to(torch.bfloat16).cuda()
                    video_prefix = "".join([f"Frame{i+1}: <image>\n" for i in range(len(num_patches_list))])
                    question = video_prefix + contexts
                    response, history = self.model.chat(self.tokenizer, pixel_values, question, gen_kwargs, num_patches_list=num_patches_list, history=None, return_history=True)
            res.append(response)
            pbar.update(1)
        pbar.close()
        return res

    def loglikelihood(self, requests: List[Instance]) -> List[Tuple[float, bool]]:
        assert False, "Not implemented yet."
        
    def generate_until_multi_round(self, requests) -> List[str]:
        raise NotImplementedError("TODO: Implement multi-round generation")
