# Utils package initialization
from loguru import logger as eval_logger

# Import all utility functions from the main utils.py file
from lmms_eval.utils import (
    get_datetime_str,
    get_file_datetime,
    get_file_task_name,
    get_results_filenames,
    get_sample_results_filenames,
    handle_non_serializable,
    hash_string,
    sanitize_list,
    sanitize_model_name,
    sanitize_task_name,
    make_table,
    simple_parse_args_string,
    # Add other commonly used functions
    get_git_commit_hash,
    create_iterator,
    run_task_tests,
    positional_deprecated,
    sanitize_long_string,
    handle_arg_string,
    is_json,
    join_iters,
    get_latest_filename,
    ignore_constructor,
)

# Make eval_logger available for import
__all__ = [
    'eval_logger',
    'get_datetime_str',
    'get_file_datetime', 
    'get_file_task_name',
    'get_results_filenames',
    'get_sample_results_filenames',
    'handle_non_serializable',
    'hash_string',
    'sanitize_list',
    'sanitize_model_name',
    'sanitize_task_name',
    'make_table',
    'simple_parse_args_string',
    'get_git_commit_hash',
    'create_iterator',
    'run_task_tests',
    'positional_deprecated',
    'sanitize_long_string',
    'handle_arg_string',
    'is_json',
    'join_iters',
    'get_latest_filename',
    'ignore_constructor',
]
