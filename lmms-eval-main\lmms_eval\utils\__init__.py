# Utils package initialization
from loguru import logger as eval_logger

# Import utility functions by importing the utils module directly
import importlib.util
import os

# Get the path to the utils.py file in the parent directory
utils_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'utils.py')
spec = importlib.util.spec_from_file_location("utils_module", utils_path)
utils_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(utils_module)

# Import the functions we need
get_datetime_str = utils_module.get_datetime_str
get_file_datetime = utils_module.get_file_datetime
get_file_task_name = utils_module.get_file_task_name
get_results_filenames = utils_module.get_results_filenames
get_sample_results_filenames = utils_module.get_sample_results_filenames
handle_non_serializable = utils_module.handle_non_serializable
hash_string = utils_module.hash_string
sanitize_list = utils_module.sanitize_list
sanitize_model_name = utils_module.sanitize_model_name
sanitize_task_name = utils_module.sanitize_task_name
make_table = utils_module.make_table
simple_parse_args_string = utils_module.simple_parse_args_string
get_git_commit_hash = utils_module.get_git_commit_hash
create_iterator = utils_module.create_iterator
run_task_tests = utils_module.run_task_tests
positional_deprecated = utils_module.positional_deprecated
sanitize_long_string = utils_module.sanitize_long_string
handle_arg_string = utils_module.handle_arg_string
is_json = utils_module.is_json
join_iters = utils_module.join_iters
get_latest_filename = utils_module.get_latest_filename
ignore_constructor = utils_module.ignore_constructor
load_yaml_config = utils_module.load_yaml_config
import_function = utils_module.import_function
pattern_match = utils_module.pattern_match
Grouper = utils_module.Grouper
MultiChoice = utils_module.MultiChoice
group = utils_module.group
general_detokenize = utils_module.general_detokenize
stop_sequences_criteria = utils_module.stop_sequences_criteria
MultiTokenEOSCriteria = utils_module.MultiTokenEOSCriteria
get_dtype = utils_module.get_dtype

# Make eval_logger available for import
__all__ = [
    'eval_logger',
    'get_datetime_str',
    'get_file_datetime', 
    'get_file_task_name',
    'get_results_filenames',
    'get_sample_results_filenames',
    'handle_non_serializable',
    'hash_string',
    'sanitize_list',
    'sanitize_model_name',
    'sanitize_task_name',
    'make_table',
    'simple_parse_args_string',
    'get_git_commit_hash',
    'create_iterator',
    'run_task_tests',
    'positional_deprecated',
    'sanitize_long_string',
    'handle_arg_string',
    'is_json',
    'join_iters',
    'get_latest_filename',
    'ignore_constructor',
    'load_yaml_config',
    'import_function',
    'pattern_match',
    'Grouper',
    'MultiChoice',
    'group',
    'general_detokenize',
    'stop_sequences_criteria',
    'MultiTokenEOSCriteria',
    'get_dtype',
]
