dataset_path: NCSOFT/K-LLaVA-W
dataset_kwargs:
  token: True
task: "llava_in_the_wild_ko"
test_split: test
output_type: generate_until
doc_to_visual: !function utils_ko.llava_doc_to_visual
doc_to_text: !function utils_ko.llava_doc_to_text
doc_to_target: "gpt_answer"
generation_kwargs:
  until:
    - "ASSISTANT:"
  image_aspect_ratio: original
  max_new_tokens: 4096
  temperature: 0
  top_p: 1.0
  num_beams: 1
  do_sample: false
process_results: !function utils_ko.llava_process_results
metric_list:
  - metric: gpt_eval_llava_all
    aggregation: !function utils_ko.llava_all_aggregation
    higher_is_better: true
  - metric: gpt_eval_llava_conv
    aggregation: !function utils_ko.llava_conv_aggregation
    higher_is_better: true
  - metric: gpt_eval_llava_detail
    aggregation: !function utils_ko.llava_detail_aggregation
    higher_is_better: true
  - metric: gpt_eval_llava_complex
    aggregation: !function utils_ko.llava_complex_aggregation
    higher_is_better: true
metadata:
  version: 0.0
  gpt_eval_model_name: "gpt-4-0613"
lmms_eval_specific_kwargs:
  default:
    pre_prompt: ""
    post_prompt: ""
