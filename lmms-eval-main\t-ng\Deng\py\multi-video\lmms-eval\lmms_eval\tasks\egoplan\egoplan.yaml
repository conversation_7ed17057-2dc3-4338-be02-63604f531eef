dataset_path: EgoLife-v1/EgoPlan
dataset_kwargs:
  token: True
  cache_dir: egoplan
  video: True
  # From_YouTube: True
task: egoplan
test_split: validation
output_type: generate_until
doc_to_visual: !function utils.egoplan_doc_to_visual
doc_to_text: !function utils.egoplan_doc_to_text
doc_to_target: "answer"
generation_kwargs:
  max_new_tokens: 4096
  temperature: 0
  top_p: 1.0
  num_beams: 1
  do_sample: false
# The return value of process_results will be used by metrics
process_results: !function utils.egoplan_process_results
# Note that the metric name can be either a registed metric function (such as the case for GQA) or a key name returned by process_results
metric_list:
  - metric: egoplan_mcq_accuracy
    aggregation: !function utils.egoplan_aggregate_results
    higher_is_better: true
lmms_eval_specific_kwargs:
  default:
    pre_prompt: ""
    post_prompt: "\nAnswer with the option's letter from the given choices directly."
  gpt4v:
    pre_prompt: ""
    post_prompt: "\nAnswer the question with A, B, C, or D."
  # qwen_vl:  
  #   pre_prompt: ""
  #   post_prompt: " Answer:"
  # otterhd:
  #   pre_prompt: ""
  #   post_prompt: " Answer:"
  xcomposer2_4khd:
    pre_prompt: "[UNUSED_TOKEN_146]user\n"
    post_prompt: " Answer this question with A, B, C, or D.[UNUSED_TOKEN_145]\n[UNUSED_TOKEN_146]assistant\n"
metadata:
  version: 0.0
