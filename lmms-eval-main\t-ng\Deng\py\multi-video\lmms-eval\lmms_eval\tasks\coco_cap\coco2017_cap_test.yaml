dataset_path: lmms-lab/COCO-Caption2017
dataset_kwargs:
  token: True
task : "coco2017_cap_test"
group : "coco_caption2017"
test_split: test
output_type: generate_until
doc_to_visual: !function utils.coco_doc_to_visual
doc_to_text: !function utils.coco_doc_to_text
doc_to_target: "answer"
generation_kwargs:
  max_new_tokens: 128
  temperature: 0
  top_p: 1.0
  num_beams: 1
  do_sample: false
process_results: !function utils.coco_test_process_result
# Note that the metric name can be either a registed metric function (such as the case for GQA) or a key name returned by process_results
metric_list:
  - metric: coco_passthrough 
    aggregation : !function utils.coco_test_aggregation_result
    higher_is_better : true
metadata:
  - version: 0.0