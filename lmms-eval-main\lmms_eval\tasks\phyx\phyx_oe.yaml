dataset_path: Cloudriver/PhyX
dataset_name: data_for_llms_eval
dataset_kwargs:
  token: True
task: "phyx_oe"
test_split: phyx_oe
output_type: generate_until
doc_to_visual: !function lmms_eval.tasks.phyx.utils.phyx_doc_to_visual
doc_to_text: !function lmms_eval.tasks.phyx.utils.phyx_doc_to_text
doc_to_target: "answer"
process_results: !function lmms_eval.tasks.phyx.utils.phyx_process_results
metric_list:
  - metric: eval_results
    aggregation: !function lmms_eval.tasks.phyx.utils.phyx_aggregate_results
    higher_is_better: true
