dataset_path: AI4Math/MathVista
dataset_kwargs:
  token: True
task: "mathvista_test"
test_split: test
output_type: generate_until
doc_to_visual: !function utils.mathvista_doc_to_visual
doc_to_text: !function utils.mathvista_doc_to_text
doc_to_target: "answer"
generation_kwargs:
  max_new_tokens: 1024
  temperature: 0
  top_p: 1.0
  num_beams: 1
  do_sample: false
process_results: !function utils.mathvista_process_results
metric_list:
  - metric: submission
    aggregation: !function utils.mathvista_aggregate_results
    higher_is_better: true

lmms_eval_specific_kwargs:
  default:
    shot_type: "reason-first" # can be "reason-first", "solution", "step-by-step"