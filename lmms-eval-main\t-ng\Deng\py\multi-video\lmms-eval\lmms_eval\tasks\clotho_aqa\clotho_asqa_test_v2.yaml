task: "clotho_asqa_test_v2"
dataset_name: "clotho_asqa_test_v2"
test_split: test
generation_kwargs:
  max_new_tokens: 256
  temperature: 0
  do_sample: False
lmms_eval_specific_kwargs:
  default:
    pre_prompt: ""
    post_prompt: ""
metric_list:
  - metric: gpt_eval
    aggregation: !function utils.clotho_aqa_v2_aggregate_results
    higher_is_better: true

process_results: !function utils.clotho_aqa_v2_process_results

include: _default_template_yaml
