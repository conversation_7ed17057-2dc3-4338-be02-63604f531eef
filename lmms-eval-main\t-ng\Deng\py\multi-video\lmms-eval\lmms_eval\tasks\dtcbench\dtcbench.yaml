dataset_path: NCSOFT/K-DTCBench
dataset_kwargs:
  token: True
task: "dtcbench"
test_split: test
output_type: generate_until
doc_to_visual: !function utils.dtcbench_doc_to_visual
doc_to_text: !function utils.dtcbench_doc_to_text
doc_to_target: "answer"
generation_kwargs:
  until:
    - "ASSISTANT:"
  image_aspect_ratio: original
# The return value of process_results will be used by metrics
process_results: !function utils.dtcbench_process_result
# Note that the metric name can be either a registed metric function (such as the case for GQA) or a key name returned by process_results
metric_list:
  - metric: dtcbench_acc
    aggregation: !function utils.dtcbench_aggregation_result
    higher_is_better: true
metadata:
  - version: 0.0