dataset_path: lmms-lab/gigaspeech
dataset_name: xs
dataset_kwargs:
  token: True
task : "gigaspeech_xs_test"
test_split: test
output_type: generate_until
doc_to_visual: !function utils.gigaspeech_doc_to_audio
doc_to_text: !function utils.gigaspeech_doc_to_text
doc_to_target: "gt"
generation_kwargs:
  max_new_tokens: 256
  temperature: 0
  top_p: 1.0
  num_beams: 1
  do_sample: false
process_results: !function utils.gigaspeech_process_result
metric_list:
  - metric: wer 
    aggregation : !function utils.gigaspeech_wer
    higher_is_better : false
metadata:
  - version: 0.0
lmms_eval_specific_kwargs:
  default:
    pre_prompt: ""
    post_prompt: ""
  qwen2_audio:
    pre_prompt: ""
    post_prompt: " <|en|>"