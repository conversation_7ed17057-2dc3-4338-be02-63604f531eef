dataset_path: lmms-lab/worldsense
dataset_kwargs:
  token: True
  cache_dir: WorldSense
  video: True
task: "worldsense"
test_split: test
output_type: generate_until
doc_to_visual: !function utils.worldsense_doc_to_visual
doc_to_text: !function utils.worldsense_doc_to_text
doc_to_target: utils.worddsense_doc_to_target
generation_kwargs:
  max_new_tokens: 1024
  temperature: 0
  top_p: 1.0
  num_beams: 1
  do_sample: false
# The return value of process_results will be used by metrics
process_results: !function utils.worldsense_process_results
# Note that the metric name can be either a registed metric function (such as the case for GQA) or a key name returned by process_results
metric_list:
  - metric: worldsense_score
    aggregation: !function utils.worldsense_aggregate_results
    higher_is_better: true
