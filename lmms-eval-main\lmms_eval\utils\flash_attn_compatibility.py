"""
Flash Attention Compatibility Utilities

This module provides utilities to handle flash_attn compatibility issues,
particularly GLIBC version mismatches that prevent flash_attn from loading.

Usage:
    # Import this module before importing transformers or any flash_attn-dependent code
    from lmms_eval.utils.flash_attn_compatibility import ensure_flash_attn_compatibility
    ensure_flash_attn_compatibility()
    
    # Then import transformers normally
    from transformers import AutoModel
"""

import os
import sys
import warnings
from typing import Optional


def check_glibc_compatibility() -> bool:
    """
    Check if the current system has compatible GLIBC version for flash_attn.
    
    Returns:
        bool: True if GLIBC is compatible, False otherwise
    """
    try:
        import subprocess
        result = subprocess.run(['ldd', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if 'GLIBC' in line and '2.31' in line:
                    # GLIBC 2.31 is known to be incompatible with some flash_attn builds
                    return False
        return True
    except Exception:
        # If we can't check, assume it's compatible
        return True


def create_dummy_flash_attn():
    """
    Create dummy flash_attn modules to prevent import errors.
    """
    class DummyFlashAttn:
        def __getattr__(self, name):
            def dummy_func(*args, **kwargs):
                raise NotImplementedError(
                    f"Flash attention function '{name}' is disabled due to compatibility issues. "
                    "The model will fall back to standard attention mechanisms."
                )
            return dummy_func
    
    # Install dummy modules
    dummy_module = DummyFlashAttn()
    sys.modules['flash_attn'] = dummy_module
    sys.modules['flash_attn.flash_attn_interface'] = dummy_module
    sys.modules['flash_attn_2_cuda'] = dummy_module
    
    # Also create specific functions that are commonly imported
    class DummyFlashAttnInterface:
        def flash_attn_unpadded_func(*args, **kwargs):
            raise NotImplementedError("Flash attention is disabled due to compatibility issues")
        
        def flash_attn_varlen_func(*args, **kwargs):
            raise NotImplementedError("Flash attention is disabled due to compatibility issues")
    
    sys.modules['flash_attn.flash_attn_interface'] = DummyFlashAttnInterface()


def set_flash_attn_env_vars():
    """
    Set environment variables to disable flash attention in various libraries.
    """
    env_vars = {
        'DISABLE_FLASH_ATTN': '1',
        'TRANSFORMERS_DISABLE_FLASH_ATTN': '1',
        'FLASH_ATTENTION_DISABLE': '1',
        'TRANSFORMERS_ATTN_IMPLEMENTATION': 'eager'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value


def ensure_flash_attn_compatibility(force_disable: bool = False, verbose: bool = True):
    """
    Ensure flash_attn compatibility by setting up fallbacks and environment variables.
    
    Args:
        force_disable (bool): If True, always disable flash_attn regardless of compatibility
        verbose (bool): If True, print information about the compatibility setup
    """
    # Set environment variables first
    set_flash_attn_env_vars()
    
    # Check if we should disable flash_attn
    should_disable = force_disable or not check_glibc_compatibility()
    
    if should_disable:
        if verbose:
            warnings.warn(
                "Flash attention is being disabled due to compatibility issues. "
                "Models will fall back to standard attention mechanisms, which may be slower but should work correctly.",
                UserWarning
            )
        
        # Create dummy modules before any potential imports
        create_dummy_flash_attn()
    
    elif verbose:
        print("Flash attention compatibility check passed.")


def test_flash_attn_import() -> bool:
    """
    Test if flash_attn can be imported successfully.
    
    Returns:
        bool: True if flash_attn imports successfully, False otherwise
    """
    try:
        import flash_attn
        return True
    except ImportError:
        return False


def get_recommended_attn_implementation() -> str:
    """
    Get the recommended attention implementation based on system compatibility.
    
    Returns:
        str: Recommended attention implementation ("flash_attention_2", "sdpa", or "eager")
    """
    if test_flash_attn_import():
        return "flash_attention_2"
    
    # Check PyTorch version for SDPA support
    try:
        import torch
        from packaging import version
        if version.parse(torch.__version__) >= version.parse("2.1.2"):
            return "sdpa"
    except ImportError:
        pass
    
    return "eager"


# Auto-setup when module is imported
if __name__ != "__main__":
    # Only auto-setup if not running as main script
    ensure_flash_attn_compatibility(verbose=False)
