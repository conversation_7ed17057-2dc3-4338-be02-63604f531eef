dataset_name: "MC_PPL"
task: "egoschema_mcppl"
test_split: test
output_type: multiple_choice
doc_to_visual: !function utils.egoschema_doc_to_visual
doc_to_text: "question"
doc_to_target: !function utils.egoschema_doc_to_answer
doc_to_choice: !function utils.egoschema_doc_to_choice
process_results: !function utils.egoschema_process_results
metric_list:
  - metric: submission
    aggregation: !function utils.egoschema_aggregate_mc_ppl
    higher_is_better: true
include: _default_template_yaml
